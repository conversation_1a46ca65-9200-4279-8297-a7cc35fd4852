<header class="header">

  <div class="bg-secondary">
    <div class="container">
      <div class="row">
        <div class="col-12 text-end">
          <nav class="top-nav" aria-label="{{ wp_get_nav_menu_name('primary_navigation') }}">
            {!! wp_nav_menu(['menu' => 5, 'menu_class' => 'nav', 'echo' => false]) !!}
          </nav>
        </div>
      </div>
    </div>
  </div> 

  <div class="container">
    <div class="row align-items-center">

      <div class="col-8 col-md-3 d-flex align-items-center order-1 px-0">
        <a class="w-100" href="{{ home_url('/') }}">
          <img src="@asset('images/mocks/footer-logo.png')" alt="" class="w-100 thickish-right-border p-3" style="min-width: 130px;">
        </a>
        <img src="@asset('images/mocks/SIM.png')" alt="" class="w-100 py-3 px-0 ms-3">
      </div>

      <div id="desktop-menu" class="col-12 col-md order-3 d-none d-md-flex align-items-end justify-content-end">
        {!! wp_nav_menu([
          'theme_location' => 'primary_navigation',
          'menu_class' => 'navbar-nav mb-2 mb-md-0',
          'walker' => new \App\BootstrapNav(),
        ]) !!}
      </div>

      <div id="mobile-menu-toggle" class="col order-3 d-md-none text-end">
        <a href="#" class="mobile-menu-toggle">
          <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" width="40" height="40" viewBox="0 0 50 50">
            <path d="M 0 7.5 L 0 12.5 L 50 12.5 L 50 7.5 Z M 0 22.5 L 0 27.5 L 50 27.5 L 50 22.5 Z M 0 37.5 L 0 42.5 L 50 42.5 L 50 37.5 Z"></path>
          </svg>
        </a>
      </div>

      <div id="mobile-menu" class="col-12 order-4 d-md-none">

        {!! wp_nav_menu([
          'theme_location' => 'primary_navigation',
          'menu_class' => 'navbar-nav nav-mobile d-none mb-2 mb-lg-0',
          'walker' => new \App\BootstrapNav(),
        ]) !!}
{{--        <nav class="nav-primary nav-mobile d-none p-3" aria-label="{{ wp_get_nav_menu_name('primary_navigation') }}">--}}
{{--          {!! wp_nav_menu(['theme_location' => 'primary_navigation', 'menu_class' => 'nav d-flex flex-column align-items-start mt-2', 'echo' => false]) !!}--}}
{{--        </nav>--}}
      </div>

      <div class="col-2 d-none d-lg-flex justify-content-center align-items-center order-2 order-md-3 p-3">
          <img src="@asset('images/mocks/gilgen.png')" alt="" class="w-75">
      </div>
    </div>
  </div>
</header>
