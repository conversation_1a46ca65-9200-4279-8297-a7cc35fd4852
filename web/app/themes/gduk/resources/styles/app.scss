@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');


// Default variable overrides

$black: #000;
$black-lighter: #343434;
$white: #fff;
$white-opactity: rgba(242, 242, 242, 0.85);
$primary: #FFDE00;
$primary-darker: #e6c800;
$secondary: #EDEDEF;
$gray-opactity: rgba(237, 237, 239, 0.85);

$body-bg: $white;
$body-color: $black;

$btn-border-radius: 0;

$aspect-ratios: (
  "1x1": 100%,
  "4x3": calc(3 / 4 * 100%),
  "16x9": calc(9 / 16 * 100%),
  "21x9": calc(9 / 21 * 100%),
  "9x16": 200%
);

$accordion-border-width:                  0;

$accordion-button-padding-y:              2rem;
$accordion-button-color:                  $black;
$accordion-button-bg:                     $white;
$accordion-button-active-bg:              $white;
$accordion-button-active-color:           $black;

$accordion-button-focus-border-color: transparent;
$accordion-button-focus-box-shadow: none;

$accordion-icon-color: $black;
$accordion-icon-active-color: $primary;

$accordion-button-icon: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-plus" viewBox="0 0 16 16"><path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/></svg>');
$accordion-button-active-icon: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-dash" viewBox="0 0 16 16"><path d="M4 8a.5.5 0 0 1 .5-.5h7a.5.5 0 0 1 0 1h-7A.5.5 0 0 1 4 8z"/></svg>');

// Optional
// 3. Include remainder of required Bootstrap stylesheets (including any separate color mode stylesheets)

@import "bootstrap";

// 4. Include any default map overrides here

$custom-theme-colors: (
  "primary": $primary,
  "secondary": $secondary,
);

$theme-colors: map-merge($theme-colors, $custom-theme-colors);
//$theme-colors-rgb: map-loop($theme-colors, to-rgb, "$value");
//$utilities-colors: map-merge($utilities-colors, $theme-colors-rgb);
//$utilities-text-colors: map-loop($utilities-colors, rgba-css-var, "$key", "text");
//$utilities-bg-colors: map-loop($utilities-colors, rgba-css-var, "$key", "bg");



html, body {
  font-family: 'Inter', sans-serif;
}

span, p {
  font-size: 15px;
}

h1, h2, h3, h4, h5, h6 {
  font-weight: 400;
}

a {
  font-size: 13px;
  font-weight: 500;
  line-height: 27.77px;
  color: $black;
}

* + p:last-child {
  margin-bottom: 0;
}

.object-fit-cover {
  object-fit: cover;
}


.bg-primary-right {
  background: $primary;
}

.bg-secondary-right {
  background: $secondary;
}

@include media-breakpoint-up(md) {
  .bg-primary-right {
    background: linear-gradient(to left, $primary 85%, transparent 25%) no-repeat;
  }

  .bg-secondary-right {
    background: linear-gradient(to left, $secondary 85%, transparent 25%) no-repeat;
  }
}

.bg-primary-offset {
  background: linear-gradient(to right, $primary 45%, transparent 45%) no-repeat;
}

.bg-black-offset {
  background: linear-gradient(to right, $black 45%, transparent 45%) no-repeat;
}

.bg-primary-offset-to-white {
  background: linear-gradient(to right, $primary 45%, transparent 45%) no-repeat;
}

.bg-white-offset {
  background: linear-gradient(to right, $white 45%, transparent 45%) no-repeat;
}

.bg-off-white-vertical {
  background: linear-gradient(180deg, $gray-opactity 70%, transparent 30%);
}

.bg-white-opacity {
  background: $gray-opactity;
}

.bg-black.text-white {
  color: $white;

  a:not(.text-dark) {
    color: $primary !important;
    font-weight: bold;
  }

  a.text-dark {
    color: #fff !important;
  }
}

a:not([class]) {
    font-weight: bold;
    font-size: 1em;
}

.g-icon {
  width: 65px;
  height: 65px;
}

.gif-icon {
  height: 130px;
}

.svg-icon {
  width: 80px;
}

.ratio.ratio-4x3 img {
  height: 100%;
  width: 100%;
  object-fit: cover;
}

.g-lis {
  li {
    list-style-image: url('../images/mocks/G-black-yellow.png');
    font-size: 17px;
    line-height: 2rem;

    span {
      display: inline-block;
      vertical-align: super;
    }

    a {
      font-size: 17px;
      font-weight: bold;
    }
  }
}

.arrow-icon {
  width: 40px;
}

.arrow-lis li {
  list-style: none;
  margin: 0.2rem 0;

  span {
    padding-left: 0.5rem;
  }

  &::before {
    color: $black;
    content: "▶";
    display: inline-block;
    font-weight: 700;
    margin-left: -1em;
    width: 1em;
    //vertical-align: text-bottom;
  }
}

#banner h1 {
  font-size: calc(1rem + 1.5vw);
}

// Clip the banner image for mobile/desktop
.banner-clip {
  clip-path: polygon(0 0, 100% 0, 100% 85%, 0 100%);
}

@include media-breakpoint-up(md) {
  .banner-clip {
    clip-path: polygon(0 0, 100% 0%, 85% 100%, 0% 100%);
  }
}

div.wpforms-container-full .wpforms-form button[type="submit"].btn {
  //--bs-btn-padding-x:0.75rem;
  //--bs-btn-padding-y:0.375rem;
  //--bs-btn-font-family: ;
  //--bs-btn-font-size:1rem;
  //--bs-btn-font-weight:400;
  //--bs-btn-line-height:1.5;
  //--bs-btn-color:#222;
  //--bs-btn-bg:#0000;
  //--bs-btn-border-width:1px;
  //--bs-btn-border-color:#0000;
  //--bs-btn-border-radius:0;
  //--bs-btn-hover-border-color:#0000;
  //--bs-btn-box-shadow:inset 0 1px 0 #f2f2f226,0 1px 1px rgba(34,34,34,.075);
  //--bs-btn-disabled-opacity:0.65;
  //--bs-btn-focus-box-shadow:0 0 0 0.25rem rgba(var(--bs-btn-focus-shadow-rgb),.5);
  border-radius:0;
  cursor:pointer;
  font-family:var(--bs-btn-font-family);
  font-size:1rem;
  font-weight:400;
  line-height:1.5;
  padding:.375rem .75rem;
  text-align:center;
  text-decoration:none;
  transition:color .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out;
  user-select:none;
  vertical-align:middle
}

div.wpforms-container-full .wpforms-form button[type="submit"].btn-primary {
  background-color: $primary;
  border: 1px solid $primary;
  color: $black;

  &:hover {
    border: 1px solid $primary;
    background-color: $primary-darker;
  }
}

div.wpforms-container-full .wpforms-form button[type="submit"].btn-dark.btn-outline-primary {
  background-color: $black;
  border: 1px solid $primary;
  color: $primary;
  text-transform: uppercase;

  &:hover {
    background-color: $black-lighter;
  }
}

.btn-dark:hover {
  background-color: $black-lighter;
}

.large-text {
  font-size: 18px !important;

  * {
    font-size: 18px !important;
  }

  a {
    font-size: 24px !important;
  }
}

.text-decoration-underline-none {
  text-decoration: none;
}

.thick-left-border {
  border-left: 5px solid $black;
}

.thickish-left-border {
  border-left: 3px solid $black;
}

.thickish-right-border {
  border-right: 3px solid $black;
}

.thick-bottom-border {
  border-bottom: 5px solid $black;
}

hr.thick-yellow {
  border-top: 5px solid $primary;
  opacity: 1;
  width: 40px;
}

hr.thick-white {
  border-top: 5px solid $white;
  opacity: 1;
  width: 66px;
}

hr.thick-black {
  border-top: 5px solid $black;
  opacity: 1;
  width: 40px;
}

.bg-black.text-white hr.thick-black {
  border-top: 5px solid $white;
  opacity: 1;
  width: 40px;
}

hr.thick-black-line {
  opacity: 1;
  width: 162px;
  border-top: 5px solid $black;
}

.navbar-nav li a.active {
  color: black !important;
}

#desktop-menu {
  height: 100%;
  min-height: 100px;

  ul.navbar-nav {
    list-style: none;
    //padding-left: 0;
    display: flex;
    height: 100%;
    margin-bottom: 0;
    flex-direction: row;
    justify-content: center;
    padding-bottom: 1rem;
    gap: 0 1.5rem;
    align-items: center;
    flex-wrap: wrap;

    .menu-contact{
      background-color: rgba(255, 222, 0, 1) !important;
      padding: 0 15px;

      a{
        font-weight: bold;
      }
    }

    .menu-shop{
      background-color: #000 !important;
      padding: 0 15px;

      > a{
        color: #fff !important;
        font-weight: bold;
      }
    }

  }

  .navbar-nav .dropdown-menu {
    position: absolute;
    left: 0;
  }

  li.dropdown:hover > ul.dropdown-menu {
    display: block !important;
  }

  ul.navbar-nav > li {
    position: relative;
  }

  ul.navbar-nav > li + li:before {
    content: "|";
    font-weight: bold;
    margin: 0 0.4rem;
    position: absolute;
    left: -1.5rem;
    top: 24%;
  }

  ul.navbar-nav > li {
    font-weight: 500;
    //display: flex;
  }

  ul.navbar-nav li a {
    color: $black;
    font-size: 13px;
    text-decoration: none;
    font-weight: 500;

    &:hover {
      text-decoration: underline;
    }
  }
}

#mobile-menu {

  li a {
    font-size: 15px;
    font-weight: 500;
  }

  li.dropdown:hover > ul.dropdown-menu {
    display: block !important;
  }
}

.top-nav {
  height: 100%;

  ul {
    list-style: none;
    padding-left: 0;
    display: flex;
    align-items: center;
    height: 100%;
    margin-bottom: 0;
    flex-wrap: wrap;
    justify-content: flex-end;
  }

  ul li {
    //font-weight: bold;
    margin-left: 1rem;
  }

  ul li a {
    color: $black;
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }
}

section#news-and-events {
  .equalize-me-container {
    .position-relative.h-100 {
      > div {
        height: 300px;

        > div {
          height: 100%;

          a.btn.btn-primary {
            position: absolute;
            bottom: 25px;
          }
        }
      }
    }
  }
}

@media screen and (max-width: 1399px) {
  section#news-and-events {
    .equalize-me-container {
      .position-relative.h-100 {
        > div {
          height: 380px;
        }
      }
    }
  }
}

@media screen and (max-width: 1199px) {
  section#news-and-events {
    .equalize-me-container {
      .position-relative.h-100 {
        > div {
          height: 470px;
        }
      }
    }
  }
}

@media screen and (max-width: 991px) {
  section#news-and-events {
    .equalize-me-container {
      .position-relative.h-100 {
        > div {
          height: 300px;
        }
      }
    }
  }
}

@media screen and (max-width: 767px) {
  section#news-and-events {
    .equalize-me-container {
      .position-relative.h-100 {
        > div {
          height: unset;

          > div {
            height: unset;

            a.btn.btn-primary {
              position: relative;
              bottom: unset;
            }
          }
        }
      }
    }
  }
}

ul#menu-top-navigation li a {
  font-size: 0.8rem;
}

@include media-breakpoint-up(md) {
  .nav-primary ul {
    justify-content: flex-end;
  }
}

input.search {
  border: none;
  box-shadow: none;
  font-size: 18px;
  width: 100%;
  padding: 0.5rem 1rem 0.5rem 2.5rem;
  background-color: white;
  background-image: url('../images/mocks/search.png');
  background-position: left 10px center;
  background-size: 20px 20px;
  background-repeat: no-repeat;
  background-attachment: scroll;
}

.featured-tag {
  position: relative;
  padding-top: 4rem !important;

  &::before {
    // @todo - add https://fonts.adobe.com/fonts/omnes font?
    content: "Featured story";
    background: white;
    position: absolute;
    top: 0;
    right: 0;
    padding: 0.5rem;
    z-index: 10;
  }
}

.wpforms-field-label-inline {
  color: #444444;
}

.pull-up {
  margin-top: -8rem;
}

.push-down {
  padding-top: 12rem;
}

@include media-breakpoint-up(sm) {
  .bg-clip-path {
    clip-path: polygon(0 0, 100% 0, calc(100% - 3rem) 100%, 0% 100%);
    padding-right: 5rem !important;
  }
}

.offset-bg-image {
  background-position: right;
  background-repeat: no-repeat;
  background-size: cover;
}

.taxonomy-header-offset {
  margin-top: 2rem;
  margin-bottom: -2rem;
  width: fit-content;
}

.taxonomy-item-offset {
  margin-top: -2rem;
  margin-bottom: 0rem;
}

@include media-breakpoint-up(md) {
  .offset-bg-image {
    background-size: 75%;
  }

  .taxonomy-item-offset {
    margin-top: -2rem;
    margin-bottom: 2rem;
  }
}

@import "breadcrumb";

section#subscribe div.wpforms-container-full {
  margin-bottom: 0;
}

.wp-pagenavi {
  a {
    border: 0;
  }
  a, span {
    color: $white;
    font-size: 17px;
  }
  span.current {
    font-weight: normal;
    text-decoration: underline;
  }
}

.accordion-button:not(.collapsed) {
  padding-bottom: 0;
}

.accordion-flush .accordion-item:first-child {
  border-top: 5px solid $black;
}

.accordion-item {
  border-bottom: 5px solid $black;
}

.accordion-flush .accordion-item:last-child {
  border-bottom: 5px solid $black;
}

.wp-block-image img {
  width: 100%;
}

/* WooCommerce Product Tables DataTables override */
.dataTables_wrapper .dataTables_paginate .paginate_button.current,
.dataTables_wrapper .dataTables_paginate .paginate_button.current:hover,
.dataTables_wrapper .dataTables_paginate .paginate_button {
  color: $white !important;
}

.woocommerce-message {
  border-top-color: $primary;
}

.wc-product-table td .amount,
.wc-product-table-wrapper .cart-error, .wc-product-table-wrapper .cart-success,
.woocommerce ul.products li.product .price,
.woocommerce div.product p.price, .woocommerce div.product span.price,
.woocommerce div.product .stock {
  font-weight: bolder;
  color: $black;
}

.woocommerce span.onsale {
  background: $black;
  border-radius: 0;
}

.woocommerce .quantity .qty {
  height: 38px;
}

.wc-product-table .wpt_variations_form .variations select, .woocommerce .wc-product-table .wpt_variations_form .variations select {
  height: 36px;
  margin-bottom: 0;
}

.wc-product-table th.col-sku {
  min-width: 100px !important;
}

.wc-product-table .multi-cart .multi-cart-check input[type="checkbox"] {
  transform: scale(1.25);
}

.wc-product-table .product a.button,
.wc-product-table .product .add-to-cart-wrapper a.button,
.wc-product-table .product .add-to-cart-wrapper button.button,
.woocommerce button.button.alt,
.woocommerce ul.products li.product .button,
.woocommerce div.product form.cart .button {
  background-color: $primary;
  border: 1px solid $primary;
  color: $black;
  height: 39px;

  &:hover {
    border: 1px solid $primary;
    background-color: $primary-darker;
    color: $black;
  }
}

.woocommerce ul.products li.product .button.wc-quick-view-button.with-icon {
  margin-top: 0;
}

.wc-product-table .product .add-to-cart-wrapper a.button.disabled,
.wc-product-table .product .add-to-cart-wrapper button.button.disabled,
.woocommerce button.button.alt.disabled,
.woocommerce ul.products li.product .button.disabled,
.woocommerce div.product form.cart .button.disabled {
  background-color: #737373;
  border: 1px solid #737373;
  color: $black;
}

.wc-product-table-controls .wc-product-table-multi-form input[type="submit"] {
  background-color: $black;
  border: 1px solid $black;
  color: $white;

  &:hover {
    background-color: $black-lighter;
    color: $white;
  }
}

.woocommerce ul.products li.product .woocommerce-loop-product__title,
.single-product-link {
  font-size: 15px;
}

.single-product-link {
  font-weight: bold;
}

section.upsells ul.products {
  display: flex;
  flex-direction: row;
}

section.upsells ul.products li.product {
  display: flex;
  flex-direction: column;
  float: none;
}

section.upsells ul.products li.product .button.wc-quick-view-button {
  margin-top: auto !important;
}
section.upsells ul.products li.product .button {
  margin-top: 1rem;
  width: fit-content;
}

section.upsells ul.products,
section.upsells ul.products li.product {
  margin-bottom: 0;
}

.select2-container .select2-selection--single{
  height: 40px !important;
}

.select2-container--default .select2-selection--single .select2-selection__arrow{
  height: 40px !important;
}

.select2-container--default .select2-selection--single .select2-selection__rendered{
  line-height: 40px !important;
}

.woocommerce form .form-row .input-text,
.woocommerce-page form .form-row .input-text {
  padding: 10px;
}

.wpforms-container.wpf-center {
  margin: 0 auto !important;
  /* Adjust the width in the next 2 lines as your site needs */
  max-width: 600px !important;
  width: 600px !important;
}

/* Readjust the form width for smaller devices */
@media only screen and (max-width: 700px) {

  .wpforms-container.wpf-center {
    /* Reset the width for devices under 600px */
    max-width: unset !important;
    width: auto !important;
  }

}

#testimonials{
  .items-cetner{
    align-items: center;
    position: relative;

    // .quotes-left{
    //   position: absolute;
    //   top: 100px;
    // }

    // .quotes-right{
    //   position: absolute;
    //   bottom: 100px;
    //   right: 0;
    // }
  }
}

// @media screen and (max-width: 1199px) {
//   #testimonials{
//     .items-cetner{
//       .quotes-left{
//         top: 50px;
//       }

//       .quotes-right{
//         bottom: 50px;
//       }
//     }
//   }
// }

// @media screen and (max-width: 991px) {
//   #testimonials{
//     .items-cetner{
//       .quotes-left{
//         position: relative;
//         top: unset;
//         left: unset;
//         padding-top: 30px;
//       }

//       .quotes-right{
//         position: relative;
//         bottom: unset;
//         right: unset;
//         padding-bottom: 30px;
//       }
//     }
//   }
// }

// Hide registration prompt when logged in
body.woocommerce-shop.logged-in #main > #fullwidth {
  display: none
}

.facetwp-counter {
  display: none;
}

.facetwp-facet {
  margin-bottom: 2rem;
}

.facetwp-facet-pager_ a {
  color: white;
  font-size: 1rem;
}

.facetwp-radio {
  background: none !important;
  display: inline-block;
  line-height: 1em;
  padding: 10px 12px; /* top/bottom right/left */
  margin: 0 8px 8px 0; /* top right bottom left */
  background-color: white !important;
  border: 1px solid white;
  color: #000;
  border-radius: 3px;
}

/* Style the hover and 'checked' states */
.facetwp-radio:hover,
.facetwp-radio.checked {
  background-image: none !important;
  background-color: $primary-darker !important;
  border-color: $primary-darker;
  color: #000 !important;
}

