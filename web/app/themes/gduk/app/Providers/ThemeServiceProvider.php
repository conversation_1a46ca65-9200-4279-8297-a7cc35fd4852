<?php

namespace App\Providers;

use Roots\Acorn\Sage\SageServiceProvider;
use App\Includes\WooCommerceHooks;
use App\Includes\WordPressHooks;

class ThemeServiceProvider extends SageServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        parent::register();

        // Register WordPressHooks as a singleton
        $this->app->singleton(WordPressHooks::class, function ($app) {
            return new WordPressHooks($app);
        });

        // Register WooCommerceHooks with WordPressHooks dependency injection
        $this->app->singleton(WooCommerceHooks::class, function ($app) {
            return new WooCommerceHooks($app, $app->make(WordPressHooks::class));
        });
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        parent::boot();
    }
}
