<?php

namespace App\Providers;

use Roots\Acorn\Sage\SageServiceProvider;
use App\Includes\WooCommerceHooks;
use App\Includes\WordPressHooks;

class ThemeServiceProvider extends SageServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        parent::register();
    
        $this->app->register(WooCommerceHooks::class);
        $this->app->register(WordPressHooks::class);

        // Pass the application instance to the WooCommerceHooks class
        $this->app->bind(WooCommerceHooks::class, function ($app) {
            return new WordPressHooks($app);
        });
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        parent::boot();
    }
}
