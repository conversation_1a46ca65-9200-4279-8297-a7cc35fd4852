<?php

namespace App\Providers;

use Roots\Acorn\Sage\SageServiceProvider;
use App\Includes\WooCommerceHooks;
use App\Includes\WordPressHooks;

class ThemeServiceProvider extends SageServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        parent::register();

        // Register WordPressHooks first
        $this->app->register(WordPressHooks::class);

        // Register WooCommerceHooks with WordPressHooks dependency injection
        $this->app->bind(WooCommerceHooks::class, function ($app) {
            return new WooCommerceHooks($app, $app->make(WordPressHooks::class));
        });

        $this->app->register(WooCommerceHooks::class);
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        parent::boot();
    }
}
